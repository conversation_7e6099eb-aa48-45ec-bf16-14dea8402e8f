package com.chief.service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.chief.dto.CsvImportRequest;
import com.chief.dto.CsvImportResponse;

@Service
public class CsvImportService {

    private static final Logger logger = LoggerFactory.getLogger(CsvImportService.class);

    @Autowired
    private SparkSession sparkSession;

    @Value("${spring.datasource.url}")
    private String jdbcUrl;

    @Value("${spring.datasource.username}")
    private String jdbcUsername;

    @Value("${spring.datasource.password}")
    private String jdbcPassword;

    private final String uploadDir = "uploads/csv/";

    /**
     * 从MySQL导出数据到CSV（示例方法）
     * 注意：这个方法需要确保目标表存在
     */
    public void mysql2csv(){
        try {
            // 设置 JDBC 连接参数
            Properties connectionProps = new Properties();
            connectionProps.put("user", jdbcUsername);
            connectionProps.put("password", jdbcPassword);
            connectionProps.put("driver", "com.mysql.cj.jdbc.Driver");
            connectionProps.put("useUnicode", "true");
            connectionProps.put("characterEncoding", "UTF-8");
            connectionProps.put("serverTimezone", "Asia/Shanghai");

            // 使用一个通用的查询来测试连接

            Dataset<Row> dataset = sparkSession.read()
                    .jdbc(jdbcUrl, "DataAircraft", connectionProps);

            logger.info("MySQL连接测试成功，数据预览:");
            dataset.show();

            // 可以在这里添加导出到CSV的逻辑
             dataset.write().option("header", "true").csv("output/mysql_export.csv");

        } catch (Exception e) {
            logger.error("MySQL连接或查询失败: {}", e.getMessage());
            throw new RuntimeException("MySQL操作失败: " + e.getMessage(), e);
        }
    }
    /**
     * 通过文件上传导入CSV到MySQL
     */
    public CsvImportResponse importCsvFromFile(MultipartFile file, CsvImportRequest request) {
        long startTime = System.currentTimeMillis();
        List<String> errors = new ArrayList<>();

        try {
            // 验证请求参数
            if (file.isEmpty()) {
                return CsvImportResponse.failure("上传的文件为空", List.of("File is empty"));
            }

            if (request.getTableName() == null || request.getTableName().trim().isEmpty()) {
                return CsvImportResponse.failure("表名不能为空", List.of("Table name is required"));
            }

            // 创建上传目录
            createUploadDirectory();

            // 保存上传的文件
            String fileName = System.currentTimeMillis() + "_" + file.getOriginalFilename();
            Path filePath = Paths.get(uploadDir + fileName);
            Files.copy(file.getInputStream(), filePath);

            // 设置文件路径
            request.setCsvFilePath(filePath.toString());

            // 执行导入
            CsvImportResponse response = importCsvToMySQL(request);

            // 清理临时文件
            try {
                Files.deleteIfExists(filePath);
            } catch (IOException e) {
                logger.warn("Failed to delete temporary file: {}", filePath, e);
            }

            return response;

        } catch (Exception e) {
            logger.error("Error importing CSV file", e);
            errors.add("Import failed: " + e.getMessage());
            return CsvImportResponse.failure("导入失败: " + e.getMessage(), errors);
        }
    }

    /**
     * 通过文件路径导入CSV到MySQL
     */
    public CsvImportResponse importCsvFromPath(CsvImportRequest request) {
        return importCsvToMySQL(request);
    }

    /**
     * 核心导入逻辑
     */
    private CsvImportResponse importCsvToMySQL(CsvImportRequest request) {
        long startTime = System.currentTimeMillis();
        List<String> errors = new ArrayList<>();

        try {
            logger.info("Starting CSV import: {}", request);

            // 验证文件是否存在
            File csvFile = new File(request.getCsvFilePath());
            if (!csvFile.exists()) {
                return CsvImportResponse.failure("CSV文件不存在: " + request.getCsvFilePath(), 
                        List.of("File not found: " + request.getCsvFilePath()));
            }

            // 读取CSV文件
            Dataset<Row> df = sparkSession.read()
                    .option("header", request.isHasHeader())
                    .option("delimiter", request.getDelimiter())
                    .option("inferSchema", "true")
                    .option("encoding", "UTF-8")
                    .csv(request.getCsvFilePath());

            // 如果指定了列名，则重命名列
            if (request.getColumnNames() != null && !request.getColumnNames().isEmpty()) {
                String[] currentColumns = df.columns();
                if (currentColumns.length != request.getColumnNames().size()) {
                    return CsvImportResponse.failure(
                            String.format("列数不匹配: CSV有%d列，指定了%d个列名", 
                                    currentColumns.length, request.getColumnNames().size()),
                            List.of("Column count mismatch"));
                }

                // 重命名列
                for (int i = 0; i < currentColumns.length; i++) {
                    df = df.withColumnRenamed(currentColumns[i], request.getColumnNames().get(i));
                }
            }

            // 显示数据预览
            logger.info("CSV数据预览:");
            df.show(5);
            logger.info("数据schema:");
            df.printSchema();

            long recordCount = df.count();
            logger.info("总记录数: {}", recordCount);

            // 配置JDBC连接属性
            Properties connectionProperties = new Properties();
            connectionProperties.put("user", jdbcUsername);
            connectionProperties.put("password", jdbcPassword);
            connectionProperties.put("driver", "com.mysql.cj.jdbc.Driver");
            connectionProperties.put("useUnicode", "true");
            connectionProperties.put("characterEncoding", "UTF-8");
            connectionProperties.put("serverTimezone", "Asia/Shanghai");

            // 确定保存模式
            SaveMode saveMode = getSaveMode(request.getMode());

            // 写入MySQL
            df.write()
                    .mode(saveMode)
                    .jdbc(jdbcUrl, request.getTableName(), connectionProperties);

            long endTime = System.currentTimeMillis();
            long processingTime = endTime - startTime;

            logger.info("CSV导入完成: 表={}, 记录数={}, 耗时={}ms", 
                    request.getTableName(), recordCount, processingTime);

            return CsvImportResponse.success(request.getTableName(), recordCount, recordCount, processingTime);

        } catch (Exception e) {
            logger.error("CSV导入失败", e);
            errors.add("Import error: " + e.getMessage());
            long processingTime = System.currentTimeMillis() - startTime;
            
            CsvImportResponse response = CsvImportResponse.failure("导入失败: " + e.getMessage(), errors);
            response.setProcessingTimeMs(processingTime);
            return response;
        }
    }

    /**
     * 获取保存模式
     */
    private SaveMode getSaveMode(String mode) {
        if (mode == null) {
            return SaveMode.Append;
        }
        
        switch (mode.toLowerCase()) {
            case "overwrite":
                return SaveMode.Overwrite;
            case "ignore":
                return SaveMode.Ignore;
            case "error":
                return SaveMode.ErrorIfExists;
            case "append":
            default:
                return SaveMode.Append;
        }
    }

    /**
     * 创建上传目录
     */
    private void createUploadDirectory() throws IOException {
        Path uploadPath = Paths.get(uploadDir);
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
        }
    }

    /**
     * 获取CSV文件的列信息
     */
    public List<String> getCsvColumns(String filePath, boolean hasHeader, String delimiter) {
        try {
            Dataset<Row> df = sparkSession.read()
                    .option("header", hasHeader)
                    .option("delimiter", delimiter)
                    .option("inferSchema", "true")
                    .csv(filePath);

            return List.of(df.columns());
        } catch (Exception e) {
            logger.error("Failed to read CSV columns", e);
            return new ArrayList<>();
        }
    }
}
