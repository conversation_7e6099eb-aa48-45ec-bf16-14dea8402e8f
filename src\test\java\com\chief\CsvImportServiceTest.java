package com.chief;

import com.chief.dto.CsvImportRequest;
import com.chief.dto.CsvImportResponse;
import com.chief.service.CsvImportService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@TestPropertySource(properties = {
})
public class CsvImportServiceTest {

    @Autowired
    private CsvImportService csvImportService;

    @Test
    public void test1(){
        csvImportService.mysql2csv();
    }
    @Test
    public void testGetCsvColumns() {
        // 测试获取CSV列信息
        String csvPath = "src/test/resources/test-data.csv";
        List<String> columns = csvImportService.getCsvColumns(csvPath, true, ",");
        
        assertNotNull(columns);
        assertEquals(6, columns.size());
        assertTrue(columns.contains("id"));
        assertTrue(columns.contains("name"));
        assertTrue(columns.contains("age"));
        assertTrue(columns.contains("email"));
        assertTrue(columns.contains("department"));
        assertTrue(columns.contains("salary"));
    }

    @Test
    public void testImportCsvFromPath() {
        // 注意：这个测试需要MySQL数据库运行
        // 如果没有数据库，测试会失败，这是正常的
        
        CsvImportRequest request = new CsvImportRequest();
        request.setTableName("test_employees");
        request.setCsvFilePath("src/test/resources/test-data.csv");
        request.setHasHeader(true);
        request.setDelimiter(",");
        request.setMode("overwrite");
        request.setColumnNames(Arrays.asList("id", "name", "age", "email", "department", "salary"));

        try {
            CsvImportResponse response = csvImportService.importCsvFromPath(request);
            
            // 如果数据库可用，检查响应
            if (response.isSuccess()) {
                assertTrue(response.getRecordsProcessed() > 0);
                assertEquals("test_employees", response.getTableName());
                assertTrue(response.getProcessingTimeMs() > 0);
            } else {
                // 如果数据库不可用，至少确保错误信息不为空
                assertNotNull(response.getMessage());
                assertFalse(response.getMessage().isEmpty());
            }
            
        } catch (Exception e) {
            // 如果没有数据库连接，这是预期的
            assertTrue(e.getMessage().contains("Connection") || 
                      e.getMessage().contains("database") ||
                      e.getMessage().contains("mysql"));
        }
    }

    @Test
    public void testCsvImportRequestValidation() {
        CsvImportRequest request = new CsvImportRequest();
        
        // 测试默认值
        assertTrue(request.isHasHeader());
        assertEquals(",", request.getDelimiter());
        assertEquals("append", request.getMode());
        
        // 测试设置值
        request.setTableName("test_table");
        request.setHasHeader(false);
        request.setDelimiter(";");
        request.setMode("overwrite");
        
        assertEquals("test_table", request.getTableName());
        assertFalse(request.isHasHeader());
        assertEquals(";", request.getDelimiter());
        assertEquals("overwrite", request.getMode());
    }
}
