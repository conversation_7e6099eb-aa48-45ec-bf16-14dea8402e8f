<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8c57ec81-9297-469b-b007-d80bca292df5" name="Changes" comment="新增mysql2csv方法及测试用例&#10;&#10;- 在CsvImportService中添加mysql2csv方法，实现从MySQL导出数据到CSV的功能&#10;- 更新application.yml中的数据库连接配置&#10;- 在.gitignore中新增target目录忽略规则&#10;- 为CsvImportServiceTest添加test1单元测试方法验证mysql2csv功能">
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chief/service/CsvImportService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chief/service/CsvImportService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/chief/CsvImportServiceTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/chief/CsvImportServiceTest.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="downloadAnnotationsAutomatically" value="true" />
        <option name="downloadDocsAutomatically" value="true" />
        <option name="downloadSourcesAutomatically" value="true" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2yzJWQXvtCIWDlhSXO82xluDq7O" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "JUnit.CsvImportServiceTest.test1.executor": "Run",
    "Maven.spark-springboot [clean].executor": "Run",
    "Maven.spark-springboot [install].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "junie.onboarding.icon.badge.shown": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "reference.settings.project.maven.importing",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="JUnit.CsvImportServiceTest.test1">
    <configuration name="CsvImportServiceTest.test1" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="spark-springboot" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.chief.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.chief" />
      <option name="MAIN_CLASS_NAME" value="com.chief.CsvImportServiceTest" />
      <option name="METHOD_NAME" value="test1" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SparkSpringBootApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="spark-springboot" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.chief.SparkSpringBootApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.CsvImportServiceTest.test1" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26094.121" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-IU-251.26094.121" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8c57ec81-9297-469b-b007-d80bca292df5" name="Changes" comment="" />
      <created>1750831499970</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750831499970</updated>
      <workItem from="1750831500942" duration="1672000" />
      <workItem from="1750833470600" duration="5674000" />
    </task>
    <task id="LOCAL-00001" summary="新增CSV导入功能及Spark集成&#10;&#10;- 添加CsvImportController处理CSV文件上传与路径导入&#10;- 实现CsvImportService核心逻辑，支持MySQL数据写入&#10;- 创建CsvImportRequest和CsvImportResponse数据传输对象&#10;- 配置application.yml添加数据库连接和Spark相关设置&#10;- 新增.gitignore文件定义项目忽略规则&#10;- 提供静态页面index.html展示功能接口&#10;- 添加单元测试CsvImportServiceTest验证核心功能">
      <option name="closed" value="true" />
      <created>1750833278364</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750833278364</updated>
    </task>
    <task id="LOCAL-00002" summary="新增mysql2csv方法及测试用例&#10;&#10;- 在CsvImportService中添加mysql2csv方法，实现从MySQL导出数据到CSV的功能&#10;- 更新application.yml中的数据库连接配置&#10;- 在.gitignore中新增target目录忽略规则&#10;- 为CsvImportServiceTest添加test1单元测试方法验证mysql2csv功能">
      <option name="closed" value="true" />
      <created>1750836621044</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750836621044</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="新增CSV导入功能及Spark集成&#10;&#10;- 添加CsvImportController处理CSV文件上传与路径导入&#10;- 实现CsvImportService核心逻辑，支持MySQL数据写入&#10;- 创建CsvImportRequest和CsvImportResponse数据传输对象&#10;- 配置application.yml添加数据库连接和Spark相关设置&#10;- 新增.gitignore文件定义项目忽略规则&#10;- 提供静态页面index.html展示功能接口&#10;- 添加单元测试CsvImportServiceTest验证核心功能" />
    <MESSAGE value="新增mysql2csv方法及测试用例&#10;&#10;- 在CsvImportService中添加mysql2csv方法，实现从MySQL导出数据到CSV的功能&#10;- 更新application.yml中的数据库连接配置&#10;- 在.gitignore中新增target目录忽略规则&#10;- 为CsvImportServiceTest添加test1单元测试方法验证mysql2csv功能" />
    <option name="LAST_COMMIT_MESSAGE" value="新增mysql2csv方法及测试用例&#10;&#10;- 在CsvImportService中添加mysql2csv方法，实现从MySQL导出数据到CSV的功能&#10;- 更新application.yml中的数据库连接配置&#10;- 在.gitignore中新增target目录忽略规则&#10;- 为CsvImportServiceTest添加test1单元测试方法验证mysql2csv功能" />
  </component>
</project>