package com.chief.controller;

import com.chief.dto.CsvImportRequest;
import com.chief.dto.CsvImportResponse;
import com.chief.service.CsvImportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/csv")
public class CsvImportController {

    private static final Logger logger = LoggerFactory.getLogger(CsvImportController.class);

    @Autowired
    private CsvImportService csvImportService;

    /**
     * 通过文件上传导入CSV到MySQL
     */
    @PostMapping("/import/upload")
    public ResponseEntity<CsvImportResponse> importCsvFromUpload(
            @RequestParam("file") MultipartFile file,
            @RequestParam("tableName") String tableName,
            @RequestParam(value = "columnNames", required = false) String columnNames,
            @RequestParam(value = "hasHeader", defaultValue = "true") boolean hasHeader,
            @RequestParam(value = "delimiter", defaultValue = ",") String delimiter,
            @RequestParam(value = "mode", defaultValue = "append") String mode) {

        try {
            logger.info("Received CSV import request: file={}, table={}, hasHeader={}, delimiter={}, mode={}", 
                    file.getOriginalFilename(), tableName, hasHeader, delimiter, mode);

            CsvImportRequest request = new CsvImportRequest();
            request.setTableName(tableName);
            request.setHasHeader(hasHeader);
            request.setDelimiter(delimiter);
            request.setMode(mode);

            // 解析列名
            if (columnNames != null && !columnNames.trim().isEmpty()) {
                List<String> columns = Arrays.asList(columnNames.split(","));
                request.setColumnNames(columns.stream().map(String::trim).toList());
            }

            CsvImportResponse response = csvImportService.importCsvFromFile(file, request);
            
            if (response.isSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("Error processing CSV import request", e);
            CsvImportResponse errorResponse = CsvImportResponse.failure(
                    "处理请求时发生错误: " + e.getMessage(), 
                    List.of(e.getMessage()));
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 通过文件路径导入CSV到MySQL
     */
    @PostMapping("/import/path")
    public ResponseEntity<CsvImportResponse> importCsvFromPath(@RequestBody CsvImportRequest request) {
        try {
            logger.info("Received CSV import request from path: {}", request);

            CsvImportResponse response = csvImportService.importCsvFromPath(request);
            
            if (response.isSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("Error processing CSV import request from path", e);
            CsvImportResponse errorResponse = CsvImportResponse.failure(
                    "处理请求时发生错误: " + e.getMessage(), 
                    List.of(e.getMessage()));
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 获取CSV文件的列信息
     */
    @PostMapping("/analyze")
    public ResponseEntity<Map<String, Object>> analyzeCsv(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "hasHeader", defaultValue = "true") boolean hasHeader,
            @RequestParam(value = "delimiter", defaultValue = ",") String delimiter) {

        try {
            // 保存临时文件
            String tempFileName = "temp_" + System.currentTimeMillis() + "_" + file.getOriginalFilename();
            java.nio.file.Path tempPath = java.nio.file.Paths.get("uploads/temp/" + tempFileName);
            java.nio.file.Files.createDirectories(tempPath.getParent());
            java.nio.file.Files.copy(file.getInputStream(), tempPath);

            // 分析CSV文件
            List<String> columns = csvImportService.getCsvColumns(tempPath.toString(), hasHeader, delimiter);

            // 清理临时文件
            java.nio.file.Files.deleteIfExists(tempPath);

            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", true);
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());
            result.put("columns", columns);
            result.put("columnCount", columns.size());
            result.put("hasHeader", hasHeader);
            result.put("delimiter", delimiter);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("Error analyzing CSV file", e);
            Map<String, Object> errorResult = new java.util.HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(errorResult);
        }
    }

    /**
     * 获取支持的导入模式
     */
    @GetMapping("/modes")
    public ResponseEntity<Map<String, Object>> getSupportedModes() {
        Map<String, Object> modes = new java.util.HashMap<>();
        modes.put("append", "追加模式 - 将数据追加到现有表中");
        modes.put("overwrite", "覆盖模式 - 删除现有数据并插入新数据");
        modes.put("ignore", "忽略模式 - 如果表已存在则忽略");
        modes.put("error", "错误模式 - 如果表已存在则抛出错误");

        Map<String, Object> result = new java.util.HashMap<>();
        result.put("modes", modes);
        result.put("default", "append");

        return ResponseEntity.ok(result);
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new java.util.HashMap<>();
        health.put("status", "UP");
        health.put("service", "CSV Import Service");
        health.put("timestamp", java.time.LocalDateTime.now());
        return ResponseEntity.ok(health);
    }
}
