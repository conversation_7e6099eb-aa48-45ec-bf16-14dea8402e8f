server:
  port: 8080

spring:
  application:
    name: spark-springboot-app

  # 数据库配置
  datasource:
    url: ****************************************************************
    username: root
    password: cjwuu123456
    driver-class-name: com.mysql.cj.jdbc.Driver

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# Spark配置
spark:
  app:
    name: SparkSpringBootApp
  master: local[*]  # 使用本地模式，使用所有可用核心

# 日志配置
logging:
  level:
    org.apache.spark: WARN
    org.apache.hadoop: WARN
    org.spark_project: WARN
    org.apache.parquet: WARN
    parquet: WARN
    root: INFO
    com.chief: DEBUG
