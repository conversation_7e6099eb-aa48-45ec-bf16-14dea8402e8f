package com.chief.config;

import org.apache.spark.SparkConf;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.sql.SparkSession;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SparkConfig {

    @Value("${spark.app.name:SparkSpringBootApp}")
    private String appName;

    @Value("${spark.master:local[*]}")
    private String masterUri;

    @Bean
    public SparkConf sparkConf() {
        // 设置系统属性来解决Windows上的Hadoop问题
        System.setProperty("hadoop.home.dir", "D:\\tool\\winutils\\hadoop-3.3.5");

        SparkConf sparkConf = new SparkConf()
                .setAppName(appName)
                .setMaster(masterUri);

        // 配置本地模式的一些参数
        sparkConf.set("spark.sql.warehouse.dir", "file:///" + System.getProperty("java.io.tmpdir") + "/spark-warehouse");
        sparkConf.set("spark.sql.adaptive.enabled", "true");

        sparkConf.set("spark.sql.adaptive.coalescePartitions.enabled", "true");
        sparkConf.set("spark.serializer", "org.apache.spark.serializer.KryoSerializer");

        // 禁用Spark UI以避免Servlet兼容性问题
        sparkConf.set("spark.ui.enabled", "false");
        sparkConf.set("spark.ui.showConsoleProgress", "false");

        // 解决Java 17+模块系统问题
        sparkConf.set("spark.driver.extraJavaOptions",
            "--add-opens java.base/java.lang=ALL-UNNAMED " +
            "--add-opens java.base/java.lang.invoke=ALL-UNNAMED " +
            "--add-opens java.base/java.lang.reflect=ALL-UNNAMED " +
            "--add-opens java.base/java.io=ALL-UNNAMED " +
            "--add-opens java.base/java.net=ALL-UNNAMED " +
            "--add-opens java.base/java.nio=ALL-UNNAMED " +
            "--add-opens java.base/java.util=ALL-UNNAMED " +
            "--add-opens java.base/java.util.concurrent=ALL-UNNAMED " +
            "--add-opens java.base/java.util.concurrent.atomic=ALL-UNNAMED " +
            "--add-opens java.base/sun.nio.ch=ALL-UNNAMED " +
            "--add-opens java.base/sun.nio.cs=ALL-UNNAMED " +
            "--add-opens java.base/sun.security.action=ALL-UNNAMED " +
            "--add-opens java.base/sun.util.calendar=ALL-UNNAMED " +
            "--add-opens java.security.jgss/sun.security.krb5=ALL-UNNAMED");

        sparkConf.set("spark.executor.extraJavaOptions",
            "--add-opens java.base/java.lang=ALL-UNNAMED " +
            "--add-opens java.base/java.lang.invoke=ALL-UNNAMED " +
            "--add-opens java.base/java.lang.reflect=ALL-UNNAMED " +
            "--add-opens java.base/java.io=ALL-UNNAMED " +
            "--add-opens java.base/java.net=ALL-UNNAMED " +
            "--add-opens java.base/java.nio=ALL-UNNAMED " +
            "--add-opens java.base/java.util=ALL-UNNAMED " +
            "--add-opens java.base/java.util.concurrent=ALL-UNNAMED " +
            "--add-opens java.base/java.util.concurrent.atomic=ALL-UNNAMED " +
            "--add-opens java.base/sun.nio.ch=ALL-UNNAMED " +
            "--add-opens java.base/sun.nio.cs=ALL-UNNAMED " +
            "--add-opens java.base/sun.security.action=ALL-UNNAMED " +
            "--add-opens java.base/sun.util.calendar=ALL-UNNAMED " +
            "--add-opens java.security.jgss/sun.security.krb5=ALL-UNNAMED");

        // 配置Hadoop使用本地文件系统
        sparkConf.set("spark.hadoop.fs.defaultFS", "file:///");
        sparkConf.set("spark.hadoop.fs.file.impl", "org.apache.hadoop.fs.LocalFileSystem");
        sparkConf.set("spark.hadoop.fs.hdfs.impl", "org.apache.hadoop.hdfs.DistributedFileSystem");
        sparkConf.set("spark.sql.warehouse.dir", "file:///tmp/spark-warehouse");
        // 禁用Hadoop的native库警告
        sparkConf.set("spark.hadoop.io.native.lib.available", "false");
        sparkConf.set("spark.hadoop.io.native", "false");

        return sparkConf;
    }

    @Bean
    public SparkSession sparkSession() {
        return SparkSession.builder()
                .config(sparkConf())
                .getOrCreate();
    }

    @Bean
    public JavaSparkContext javaSparkContext() {
        return new JavaSparkContext(sparkSession().sparkContext());
    }
}
