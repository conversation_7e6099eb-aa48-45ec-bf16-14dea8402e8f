<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spring Boot + Spark 集成演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .api-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .api-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .api-description {
            color: #666;
            margin-bottom: 15px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .result {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .input-group {
            margin: 10px 0;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 5px;
        }
        label {
            font-weight: bold;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Spring Boot 3 + Apache Spark 集成演示</h1>
        
        <div class="api-section">
            <div class="api-title">1. 基础连接测试</div>
            <div class="api-description">测试Spark集成是否正常工作</div>
            <button onclick="testConnection()">测试连接</button>
            <div id="test-result" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">2. 综合功能演示</div>
            <div class="api-description">展示RDD操作、SQL查询、统计计算和词频统计等功能</div>
            <button onclick="runDemo()">运行演示</button>
            <div id="demo-result" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">3. 文本处理</div>
            <div class="api-description">输入文本列表，过滤长度>3的字符串并转换为大写</div>
            <div class="input-group">
                <label>输入文本（用逗号分隔）:</label>
                <input type="text" id="text-input" placeholder="例如: hello,world,hi,spark,java" value="hello,world,hi,spark,java">
            </div>
            <button onclick="processText()">处理文本</button>
            <div id="text-result" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">4. 数字统计</div>
            <div class="api-description">计算数字列表的统计信息（数量、总和、平均值）</div>
            <div class="input-group">
                <label>输入数字（用逗号分隔）:</label>
                <input type="text" id="numbers-input" placeholder="例如: 1,2,3,4,5,6,7,8,9,10" value="1,2,3,4,5,6,7,8,9,10">
            </div>
            <button onclick="calculateStats()">计算统计</button>
            <div id="stats-result" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">5. 词频统计</div>
            <div class="api-description">统计文本中每个单词的出现频率</div>
            <div class="input-group">
                <label>输入文本:</label>
                <textarea id="word-input" rows="3" placeholder="输入要统计的文本...">hello world hello spark world java spark programming</textarea>
            </div>
            <button onclick="countWords()">词频统计</button>
            <div id="word-result" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">6. SparkSQL 演示</div>
            <div class="api-description">使用SparkSQL查询结构化数据</div>
            <button onclick="runSqlDemo()">运行SQL演示</button>
            <div id="sql-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        async function makeRequest(url, method = 'GET', body = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                if (body) {
                    options.body = JSON.stringify(body);
                }
                
                const response = await fetch(url, options);
                const data = await response.json();
                return { success: true, data: data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            if (result.success) {
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.textContent = '错误: ' + result.error;
                element.style.color = 'red';
            }
        }

        async function testConnection() {
            const result = await makeRequest('/api/spark/test');
            showResult('test-result', result);
        }

        async function runDemo() {
            const result = await makeRequest('/api/spark/demo');
            showResult('demo-result', result);
        }

        async function processText() {
            const input = document.getElementById('text-input').value;
            const textArray = input.split(',').map(s => s.trim());
            const result = await makeRequest('/api/spark/process-text', 'POST', textArray);
            showResult('text-result', result);
        }

        async function calculateStats() {
            const input = document.getElementById('numbers-input').value;
            const numbers = input.split(',').map(s => parseInt(s.trim())).filter(n => !isNaN(n));
            const result = await makeRequest('/api/spark/statistics', 'POST', numbers);
            showResult('stats-result', result);
        }

        async function countWords() {
            const input = document.getElementById('word-input').value;
            const result = await makeRequest('/api/spark/word-count', 'POST', input);
            showResult('word-result', result);
        }

        async function runSqlDemo() {
            const result = await makeRequest('/api/spark/sql-demo');
            showResult('sql-result', result);
        }
    </script>
</body>
</html>
