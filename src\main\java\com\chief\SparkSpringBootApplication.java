package com.chief;

import com.chief.service.CsvImportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

@SpringBootApplication
public class SparkSpringBootApplication implements CommandLineRunner {
    @Override
    public void run(String... args) throws Exception {
        csvImportService.mysql2csv();
    }

    @Autowired
    private CsvImportService csvImportService;

    public static void main(String[] args) {
        System.setProperty("hadoop.native.io", "false");
        SpringApplication.run(SparkSpringBootApplication.class, args);
    }
}
