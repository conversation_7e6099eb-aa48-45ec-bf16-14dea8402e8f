-- 创建数据库
CREATE DATABASE IF NOT EXISTS spark_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS spark_demo_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE spark_demo;

-- 创建示例表：员工信息表
CREATE TABLE IF NOT EXISTS test_employees (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    age INT,
    email VARCHAR(200),
    department VARCHAR(100),
    salary DECIMAL(10,2)
);

-- 创建示例表：销售数据表
CREATE TABLE IF NOT EXISTS sales_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    product_name VARCHAR(200) NOT NULL,
    category VARCHAR(100),
    price DECIMAL(10,2),
    quantity INT,
    sale_date DATE,
    customer_name VARCHAR(100),
    region VARCHAR(50)
);

-- 创建示例表：用户行为数据表
CREATE TABLE IF NOT EXISTS user_behavior (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    action_type VARCHAR(50),
    page_url VARCHAR(500),
    timestamp DATETIME,
    session_id VARCHAR(100),
    device_type VARCHAR(50),
    browser VARCHAR(50)
);

-- 创建示例表：产品信息表
CREATE TABLE IF NOT EXISTS products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    product_code VARCHAR(50) UNIQUE NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    category VARCHAR(100),
    brand VARCHAR(100),
    price DECIMAL(10,2),
    cost DECIMAL(10,2),
    stock_quantity INT DEFAULT 0,
    created_date DATE,
    status VARCHAR(20) DEFAULT 'ACTIVE'
);

-- 创建示例表：订单数据表
CREATE TABLE IF NOT EXISTS orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id VARCHAR(50),
    customer_name VARCHAR(100),
    order_date DATE,
    total_amount DECIMAL(12,2),
    status VARCHAR(20),
    payment_method VARCHAR(50),
    shipping_address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入一些示例数据到员工表
INSERT IGNORE INTO test_employees (id, name, age, email, department, salary) VALUES
(1, '张三', 25, '<EMAIL>', '技术部', 8000.00),
(2, '李四', 30, '<EMAIL>', '销售部', 7500.00),
(3, '王五', 28, '<EMAIL>', '技术部', 9000.00),
(4, '赵六', 35, '<EMAIL>', '人事部', 6500.00),
(5, '钱七', 27, '<EMAIL>', '技术部', 8500.00);

-- 创建用于测试的视图
CREATE OR REPLACE VIEW employee_summary AS
SELECT 
    department,
    COUNT(*) as employee_count,
    AVG(age) as avg_age,
    AVG(salary) as avg_salary,
    MIN(salary) as min_salary,
    MAX(salary) as max_salary
FROM test_employees 
GROUP BY department;

-- 显示表结构
SHOW TABLES;
DESCRIBE test_employees;
DESCRIBE sales_data;
DESCRIBE user_behavior;
DESCRIBE products;
DESCRIBE orders;

-- 显示示例数据
SELECT * FROM test_employees;
SELECT * FROM employee_summary;
